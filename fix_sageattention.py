#!/usr/bin/env python3
"""
Script to fix SageAttention import issues in ComfyUI KJNodes
"""

import re

def fix_file():
    file_path = r"custom_nodes\comfyui-kjnodes\nodes\model_optimization_nodes.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove all individual sageattention import lines
    patterns_to_remove = [
        r'\s*from sageattention import sageattn\s*\n',
        r'\s*from sageattention import sageattn_qk_int8_pv_fp16_cuda\s*\n',
        r'\s*from sageattention import sageattn_qk_int8_pv_fp16_triton\s*\n',
        r'\s*from sageattention import sageattn_qk_int8_pv_fp8_cuda\s*\n',
    ]
    
    for pattern in patterns_to_remove:
        content = re.sub(pattern, '', content)
    
    # Add check for SAGEATTENTION_AVAILABLE before using sage functions
    content = content.replace(
        'if sage_attention != "disabled":',
        'if sage_attention != "disabled" and SAGEATTENTION_AVAILABLE:'
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Fixed SageAttention imports in model_optimization_nodes.py")

if __name__ == "__main__":
    fix_file()
